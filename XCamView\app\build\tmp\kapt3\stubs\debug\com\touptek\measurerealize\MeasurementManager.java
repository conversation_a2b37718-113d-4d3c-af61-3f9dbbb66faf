package com.touptek.measurerealize;

import java.lang.System;

/**
 * 🎨 专业级测量管理器 - 高度封装的测量功能管理类
 *
 * 核心职责：
 * 1. 管理测量状态和生命周期
 * 2. 封装TpImageView与测量功能的集成
 * 3. 提供极简的API接口给上层调用
 * 4. 处理所有复杂的交互逻辑
 *
 * 设计理念：让上层调用者只需要关心开始/停止测量，所有细节都由Manager处理
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u00a6\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b0\u0018\u0000 \u0084\u00012\u00020\u0001:\u0006\u0084\u0001\u0085\u0001\u0086\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010:\u001a\u00020\u001aJ\u0006\u0010;\u001a\u00020\u001aJ\u0006\u0010<\u001a\u00020\u001aJ\u0006\u0010=\u001a\u00020\u001aJ\u0006\u0010>\u001a\u00020\u001aJ\u0006\u0010?\u001a\u00020\u001aJ\u0006\u0010@\u001a\u00020\u001aJ\u0006\u0010A\u001a\u00020\u001aJ\u0006\u0010B\u001a\u00020CJ\u0006\u0010D\u001a\u00020CJ\u0006\u0010E\u001a\u00020\u001aJ\u0006\u0010F\u001a\u00020\u001aJ\u0006\u0010G\u001a\u00020\u001aJ\u0006\u0010H\u001a\u00020IJ\u0010\u0010J\u001a\u00020I2\u0006\u0010K\u001a\u00020\fH\u0002J\u0006\u0010L\u001a\u00020IJ\u0006\u0010M\u001a\u00020IJ\u0006\u0010N\u001a\u00020IJ\u0006\u0010O\u001a\u00020IJ\u0006\u0010P\u001a\u00020\u001aJ\b\u0010Q\u001a\u00020IH\u0002J\u000e\u0010R\u001a\u00020I2\u0006\u0010S\u001a\u00020\u0012J\u0006\u0010T\u001a\u00020\u001aJ\u0006\u0010U\u001a\u00020\u001aJ\b\u0010V\u001a\u00020WH\u0002J\b\u0010X\u001a\u00020IH\u0002J\b\u0010Y\u001a\u00020IH\u0002J\b\u0010Z\u001a\u00020IH\u0002J\b\u0010[\u001a\u00020IH\u0002J\u0006\u0010\\\u001a\u00020\u001aJ\u0006\u0010]\u001a\u00020\u001aJ\u0006\u0010^\u001a\u00020\u001aJ\u0006\u0010_\u001a\u00020\u001aJ\u0006\u0010`\u001a\u00020\u001aJ\u0006\u0010a\u001a\u00020\u001aJ\u0006\u0010b\u001a\u00020\u001aJ\u0006\u0010c\u001a\u00020\u001aJ\u0006\u0010d\u001a\u00020\u001aJ\u0006\u0010e\u001a\u00020\u001aJ\u0006\u0010f\u001a\u00020\u001aJ\u0006\u0010g\u001a\u00020\u001aJ\u0006\u0010h\u001a\u00020\u001aJ\u0006\u0010i\u001a\u00020IJ\u0006\u0010j\u001a\u00020IJ\u0006\u0010k\u001a\u00020IJ\u0006\u0010l\u001a\u00020IJ\u0006\u0010m\u001a\u00020IJ\u0006\u0010n\u001a\u00020IJ\u0006\u0010o\u001a\u00020IJ\u0006\u0010p\u001a\u00020IJ\u0006\u0010q\u001a\u00020IJ\u0006\u0010r\u001a\u00020IJ\u0006\u0010s\u001a\u00020IJ\u0006\u0010t\u001a\u00020IJ\b\u0010u\u001a\u00020IH\u0002J\b\u0010v\u001a\u00020IH\u0002J\b\u0010w\u001a\u00020IH\u0002J\b\u0010x\u001a\u00020IH\u0002J\b\u0010y\u001a\u00020IH\u0002J\b\u0010z\u001a\u00020IH\u0002J\b\u0010{\u001a\u00020IH\u0002J\b\u0010|\u001a\u00020IH\u0002J\b\u0010}\u001a\u00020IH\u0002J\b\u0010~\u001a\u00020IH\u0002J\b\u0010\u007f\u001a\u00020IH\u0002J\t\u0010\u0080\u0001\u001a\u00020IH\u0002J\t\u0010\u0081\u0001\u001a\u00020IH\u0002J\t\u0010\u0082\u0001\u001a\u00020IH\u0002J\t\u0010\u0083\u0001\u001a\u00020\u001aH\u0002R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020+X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020-X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020/X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u000201X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u000203X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u000205X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u000207X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u000209X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0087\u0001"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager;", "", "context", "Landroid/content/Context;", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "overlayView", "Lcom/touptek/measurerealize/utils/MeasurementOverlayView;", "statusTextView", "Landroid/widget/TextView;", "(Landroid/content/Context;Lcom/touptek/measurerealize/TpImageView;Lcom/touptek/measurerealize/utils/MeasurementOverlayView;Landroid/widget/TextView;)V", "activeMeasurementMode", "Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;", "angleMeasureHelper", "Lcom/touptek/measurerealize/utils/AngleMeasureHelper;", "centerCircleMeasureHelper", "Lcom/touptek/measurerealize/utils/CenterCircleMeasureHelper;", "currentBitmap", "Landroid/graphics/Bitmap;", "ellipseMeasureHelper", "Lcom/touptek/measurerealize/utils/EllipseMeasureHelper;", "fourPointAngleHelper", "Lcom/touptek/measurerealize/utils/FourPointAngleHelper;", "horizonLineMeasureHelper", "Lcom/touptek/measurerealize/utils/HorizonLineMeasureHelper;", "isAngleMeasuring", "", "isCenterCircleMeasuring", "isEllipseMeasuring", "isFourPointAngleMeasuring", "isHorizonLineMeasuring", "isInitialized", "isLineMeasuring", "isParallelLinesMeasuring", "isPointMeasuring", "isRectangleMeasuring", "isThreeRectangleMeasuring", "isThreeVerticalMeasuring", "isTwoCirclesMeasuring", "isVerticalLineMeasuring", "lineMeasureHelper", "Lcom/touptek/measurerealize/utils/LineMeasureHelper;", "parallelLinesMeasureHelper", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasureHelper;", "pointMeasureHelper", "Lcom/touptek/measurerealize/utils/PointMeasureHelper;", "rectangleMeasureHelper", "Lcom/touptek/measurerealize/utils/RectangleMeasureHelper;", "threeRectangleMeasureHelper", "Lcom/touptek/measurerealize/utils/ThreeRectangleMeasureHelper;", "threeVerticalMeasureHelper", "Lcom/touptek/measurerealize/utils/ThreeVerticalMeasureHelper;", "touchHandler", "Lcom/touptek/measurerealize/MeasurementTouchHandler;", "twoCirclesMeasureHelper", "Lcom/touptek/measurerealize/utils/TwoCirclesMeasureHelper;", "verticalLineMeasureHelper", "Lcom/touptek/measurerealize/utils/VerticalLineMeasureHelper;", "addNewAngleMeasurement", "addNewCenterCircleMeasurement", "addNewEllipseMeasurement", "addNewFourPointAngleMeasurement", "addNewHorizonLineMeasurement", "addNewLineMeasurement", "addNewParallelLinesMeasurement", "addNewPointMeasurement", "addNewRectangleMeasurement", "", "addNewThreeRectangleMeasurement", "addNewThreeVerticalMeasurement", "addNewTwoCirclesMeasurement", "addNewVerticalLineMeasurement", "cleanup", "", "clearOtherModeSelections", "activeMode", "deactivateAngleMeasurement", "deactivateFourPointAngleMeasurement", "deactivateLineMeasurement", "deactivatePointMeasurement", "deleteSelectedMeasurement", "forceOverlayDisplay", "initialize", "bitmap", "isDraggingPoint", "isMixedMeasuring", "saveCurrentSelectionState", "Lcom/touptek/measurerealize/MeasurementManager$SelectionState;", "setupFourPointHybridTouchHandler", "setupHybridTouchHandler", "setupMixedTouchHandler", "setupUnifiedScaleListener", "startAngleMeasurement", "startCenterCircleMeasurement", "startEllipseMeasurement", "startFourPointAngleMeasurement", "startHorizonLineMeasurement", "startLineMeasurement", "startParallelLinesMeasurement", "startPointMeasurement", "startRectangleMeasurement", "startThreeRectangleMeasurement", "startThreeVerticalMeasurement", "startTwoCirclesMeasurement", "startVerticalLineMeasurement", "stopAngleMeasurement", "stopCenterCircleMeasurement", "stopEllipseMeasurement", "stopFourPointAngleMeasurement", "stopHorizonLineMeasurement", "stopLineMeasurement", "stopParallelLinesMeasurement", "stopPointMeasurement", "stopRectangleMeasurement", "stopThreeRectangleMeasurement", "stopThreeVerticalMeasurement", "stopVerticalLineMeasurement", "updateCenterCircleMeasurementDisplay", "updateEllipseMeasurementDisplay", "updateFourPointOverlayDisplay", "updateHorizonLineMeasurementDisplay", "updateLineMeasurementDisplay", "updateMixedOverlayDisplay", "updateOverlayDisplay", "updateParallelLinesMeasurementDisplay", "updatePointMeasurementDisplay", "updateRectangleMeasurementDisplay", "updateThreeRectangleMeasurementDisplay", "updateThreeVerticalMeasurementDisplay", "updateTwoCirclesMeasurementDisplay", "updateVerticalLineMeasurementDisplay", "validateState", "Companion", "MeasurementMode", "SelectionState", "app_debug"})
public final class MeasurementManager {
    private final android.content.Context context = null;
    private final com.touptek.measurerealize.TpImageView imageView = null;
    private final com.touptek.measurerealize.utils.MeasurementOverlayView overlayView = null;
    private final android.widget.TextView statusTextView = null;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.MeasurementManager.Companion Companion = null;
    private static final java.lang.String TAG = "MeasurementManager";
    private final com.touptek.measurerealize.utils.AngleMeasureHelper angleMeasureHelper = null;
    private final com.touptek.measurerealize.utils.FourPointAngleHelper fourPointAngleHelper = null;
    private final com.touptek.measurerealize.utils.PointMeasureHelper pointMeasureHelper = null;
    private final com.touptek.measurerealize.utils.LineMeasureHelper lineMeasureHelper = null;
    private final com.touptek.measurerealize.utils.HorizonLineMeasureHelper horizonLineMeasureHelper = null;
    private final com.touptek.measurerealize.utils.VerticalLineMeasureHelper verticalLineMeasureHelper = null;
    private final com.touptek.measurerealize.utils.ParallelLinesMeasureHelper parallelLinesMeasureHelper = null;
    private final com.touptek.measurerealize.utils.ThreeVerticalMeasureHelper threeVerticalMeasureHelper = null;
    private final com.touptek.measurerealize.utils.RectangleMeasureHelper rectangleMeasureHelper = null;
    private final com.touptek.measurerealize.utils.ThreeRectangleMeasureHelper threeRectangleMeasureHelper = null;
    private final com.touptek.measurerealize.utils.EllipseMeasureHelper ellipseMeasureHelper = null;
    private final com.touptek.measurerealize.utils.CenterCircleMeasureHelper centerCircleMeasureHelper = null;
    private final com.touptek.measurerealize.utils.TwoCirclesMeasureHelper twoCirclesMeasureHelper = null;
    private final com.touptek.measurerealize.MeasurementTouchHandler touchHandler = null;
    private boolean isAngleMeasuring = false;
    private boolean isFourPointAngleMeasuring = false;
    private boolean isPointMeasuring = false;
    private boolean isLineMeasuring = false;
    private boolean isHorizonLineMeasuring = false;
    private boolean isVerticalLineMeasuring = false;
    private boolean isParallelLinesMeasuring = false;
    private boolean isThreeVerticalMeasuring = false;
    private boolean isRectangleMeasuring = false;
    private boolean isThreeRectangleMeasuring = false;
    private boolean isEllipseMeasuring = false;
    private boolean isCenterCircleMeasuring = false;
    private boolean isTwoCirclesMeasuring = false;
    private com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMeasurementMode;
    private android.graphics.Bitmap currentBitmap;
    private boolean isInitialized = false;
    
    public MeasurementManager(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.MeasurementOverlayView overlayView, @org.jetbrains.annotations.Nullable
    android.widget.TextView statusTextView) {
        super();
    }
    
    /**
     * 🚀 初始化测量管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🎯 开始角度测量 - 支持混合模式
     */
    public final boolean startAngleMeasurement() {
        return false;
    }
    
    /**
     * ⏹️ 停止角度测量 - 极简API
     */
    public final void stopAngleMeasurement() {
    }
    
    /**
     * 🎯 开始四点角度测量 - 支持混合模式
     */
    public final boolean startFourPointAngleMeasurement() {
        return false;
    }
    
    /**
     * ⏹️ 停止四点角度测量
     */
    public final void stopFourPointAngleMeasurement() {
    }
    
    /**
     * 🎯 开始点测量 - 支持混合模式
     */
    public final boolean startPointMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止点测量
     */
    public final void stopPointMeasurement() {
    }
    
    /**
     * 📏 开始线段测量 - 极简API
     */
    public final boolean startLineMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止线段测量
     */
    public final void stopLineMeasurement() {
    }
    
    /**
     * 📏 开始水平线测量 - 极简API
     */
    public final boolean startHorizonLineMeasurement() {
        return false;
    }
    
    /**
     * 📏 开始垂直线测量 - 极简API
     */
    public final boolean startVerticalLineMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止水平线测量
     */
    public final void stopHorizonLineMeasurement() {
    }
    
    /**
     * 🛑 停止垂直线测量
     */
    public final void stopVerticalLineMeasurement() {
    }
    
    /**
     * 📏 开始平行线测量 - 极简API
     */
    public final boolean startParallelLinesMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止平行线测量
     */
    public final void stopParallelLinesMeasurement() {
    }
    
    /**
     * 📏 开始三垂直测量 - 极简API
     */
    public final boolean startThreeVerticalMeasurement() {
        return false;
    }
    
    /**
     * 📦 开始矩形测量 - 极简API
     */
    public final boolean startRectangleMeasurement() {
        return false;
    }
    
    /**
     * 📦 添加新的矩形测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewRectangleMeasurement() {
        return null;
    }
    
    /**
     * 🔵 开始椭圆测量 - 极简API
     */
    public final boolean startEllipseMeasurement() {
        return false;
    }
    
    /**
     * 🔵 添加新的椭圆测量
     */
    public final boolean addNewEllipseMeasurement() {
        return false;
    }
    
    /**
     * ⭕ 开始中心圆测量 - 极简API
     */
    public final boolean startCenterCircleMeasurement() {
        return false;
    }
    
    /**
     * ⭕ 添加新的中心圆测量
     */
    public final boolean addNewCenterCircleMeasurement() {
        return false;
    }
    
    /**
     * ⭕⭕ 开始双圆测量 - 极简API
     */
    public final boolean startTwoCirclesMeasurement() {
        return false;
    }
    
    /**
     * ⭕⭕ 添加新的双圆测量
     */
    public final boolean addNewTwoCirclesMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止三垂直测量
     */
    public final void stopThreeVerticalMeasurement() {
    }
    
    /**
     * 🛑 停止矩形测量
     */
    public final void stopRectangleMeasurement() {
    }
    
    /**
     * 🛑 停止椭圆测量
     */
    public final void stopEllipseMeasurement() {
    }
    
    /**
     * 🛑 停止中心圆测量
     */
    public final void stopCenterCircleMeasurement() {
    }
    
    /**
     * 🎯 开始三点矩形测量 - 支持旋转的矩形测量
     */
    public final boolean startThreeRectangleMeasurement() {
        return false;
    }
    
    /**
     * 🎯 添加新的三点矩形测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewThreeRectangleMeasurement() {
        return null;
    }
    
    /**
     * 🛑 停止三点矩形测量
     */
    public final void stopThreeRectangleMeasurement() {
    }
    
    /**
     * 🎯 强制覆盖层显示 - 复制yolo_demo的成功机制
     */
    private final void forceOverlayDisplay() {
    }
    
    /**
     * 🔄 更新覆盖层显示 - 支持多角度同时显示
     */
    private final void updateOverlayDisplay() {
    }
    
    /**
     * 🔄 更新点测量覆盖层显示
     */
    private final void updatePointMeasurementDisplay() {
    }
    
    /**
     * 📏 更新线段测量覆盖层显示
     */
    private final void updateLineMeasurementDisplay() {
    }
    
    /**
     * 📏 更新水平线测量覆盖层显示
     */
    private final void updateHorizonLineMeasurementDisplay() {
    }
    
    /**
     * 📏 更新垂直线测量覆盖层显示
     */
    private final void updateVerticalLineMeasurementDisplay() {
    }
    
    /**
     * 📏 更新平行线测量覆盖层显示
     */
    private final void updateParallelLinesMeasurementDisplay() {
    }
    
    /**
     * 📏 更新三垂直测量覆盖层显示
     */
    private final void updateThreeVerticalMeasurementDisplay() {
    }
    
    /**
     * 📦 更新矩形测量覆盖层显示
     */
    private final void updateRectangleMeasurementDisplay() {
    }
    
    /**
     * 🔵 更新椭圆测量覆盖层显示
     */
    private final void updateEllipseMeasurementDisplay() {
    }
    
    /**
     * ⭕ 更新中心圆测量覆盖层显示
     */
    private final void updateCenterCircleMeasurementDisplay() {
    }
    
    /**
     * ⭕⭕ 更新双圆测量覆盖层显示
     */
    private final void updateTwoCirclesMeasurementDisplay() {
    }
    
    /**
     * 🎯 更新三点矩形测量覆盖层显示
     */
    private final void updateThreeRectangleMeasurementDisplay() {
    }
    
    /**
     * 🔄 更新四点角度覆盖层显示
     */
    private final void updateFourPointOverlayDisplay() {
    }
    
    /**
     * 🔄 更新混合覆盖层显示 - 基于数据存在性显示，支持数据持久化
     */
    private final void updateMixedOverlayDisplay() {
    }
    
    /**
     * 🚀 设置四点角度混合触摸处理器
     */
    private final void setupFourPointHybridTouchHandler() {
    }
    
    /**
     * 🚀 设置混合触摸处理器（支持缩放+测量）
     */
    private final void setupHybridTouchHandler() {
    }
    
    /**
     * 🚀 设置混合模式触摸处理器 - 智能激活对应的测量类型
     */
    private final void setupMixedTouchHandler() {
    }
    
    /**
     * 📊 状态验证方法 - 确保状态一致性（支持混合共存模式）
     */
    private final boolean validateState() {
        return false;
    }
    
    /**
     * 🔒 保存当前选中状态 - 用于删除操作保护
     */
    private final com.touptek.measurerealize.MeasurementManager.SelectionState saveCurrentSelectionState() {
        return null;
    }
    
    /**
     * 📊 检查是否在任何测量模式下（混合模式支持）
     */
    public final boolean isMixedMeasuring() {
        return false;
    }
    
    /**
     * 🔄 设置统一的缩放监听器 - 解决监听器相互覆盖问题
     */
    private final void setupUnifiedScaleListener() {
    }
    
    /**
     * 🔄 取消激活三点角度测量（保持数据，仅改变激活状态）
     */
    public final void deactivateAngleMeasurement() {
    }
    
    /**
     * 🔄 取消激活四点角度测量（保持数据，仅改变激活状态）
     */
    public final void deactivateFourPointAngleMeasurement() {
    }
    
    /**
     * � 取消激活点测量（保持数据，仅改变激活状态）
     */
    public final void deactivatePointMeasurement() {
    }
    
    /**
     * 🔄 取消激活线段测量（保持数据，仅改变激活状态）
     */
    public final void deactivateLineMeasurement() {
    }
    
    /**
     * 🔄 清除其他模式的选中状态（避免多模式选中冲突）
     */
    private final void clearOtherModeSelections(com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMode) {
    }
    
    /**
     * 🔍 检查是否正在拖拽测量点 - 支持所有测量类型
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 添加新的角度测量 - 在测量模式下生成新角度
     */
    public final boolean addNewAngleMeasurement() {
        return false;
    }
    
    /**
     * 🎯 添加新的点测量 - 在测量模式下生成新点
     */
    public final boolean addNewPointMeasurement() {
        return false;
    }
    
    /**
     * 🎯 添加新的四点角度测量 - 在测量模式下生成新四点角度
     */
    public final boolean addNewFourPointAngleMeasurement() {
        return false;
    }
    
    /**
     * 📏 添加新的线段测量 - 在测量模式下生成新线段
     */
    public final boolean addNewLineMeasurement() {
        return false;
    }
    
    /**
     * 📏 添加新的水平线测量 - 在测量模式下生成新水平线
     */
    public final boolean addNewHorizonLineMeasurement() {
        return false;
    }
    
    /**
     * 📏 添加新的垂直线测量 - 在测量模式下生成新垂直线
     */
    public final boolean addNewVerticalLineMeasurement() {
        return false;
    }
    
    /**
     * 📏 添加新的平行线测量 - 在测量模式下生成新平行线
     */
    public final boolean addNewParallelLinesMeasurement() {
        return false;
    }
    
    /**
     * 📏 添加新的三垂直测量 - 在测量模式下生成新三垂直测量
     */
    public final boolean addNewThreeVerticalMeasurement() {
        return false;
    }
    
    /**
     * 🗑️ 删除选中的测量 - 支持所有测量类型，带状态保护机制
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🧹 清理资源
     */
    public final void cleanup() {
    }
    
    /**
     * 🔒 选中状态保存数据类 - 用于删除操作保护
     */
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b!\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001Bi\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\f\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\r\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0081\u0001\u0010%\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010&\u001a\u00020\'2\b\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010)\u001a\u00020*H\u00d6\u0001J\t\u0010+\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012\u00a8\u0006,"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager$SelectionState;", "", "activeMeasurementMode", "Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;", "angleSelectedId", "", "fourPointAngleSelectedId", "pointSelectedId", "lineSelectedId", "horizonLineSelectedId", "verticalLineSelectedId", "parallelLinesSelectedId", "threeVerticalSelectedId", "twoCirclesSelectedId", "(Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getActiveMeasurementMode", "()Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;", "getAngleSelectedId", "()Ljava/lang/String;", "getFourPointAngleSelectedId", "getHorizonLineSelectedId", "getLineSelectedId", "getParallelLinesSelectedId", "getPointSelectedId", "getThreeVerticalSelectedId", "getTwoCirclesSelectedId", "getVerticalLineSelectedId", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    static final class SelectionState {
        @org.jetbrains.annotations.Nullable
        private final com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMeasurementMode = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String angleSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String fourPointAngleSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String pointSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String lineSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String horizonLineSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String verticalLineSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String parallelLinesSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String threeVerticalSelectedId = null;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String twoCirclesSelectedId = null;
        
        /**
         * 🔒 选中状态保存数据类 - 用于删除操作保护
         */
        @org.jetbrains.annotations.NotNull
        public final com.touptek.measurerealize.MeasurementManager.SelectionState copy(@org.jetbrains.annotations.Nullable
        com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMeasurementMode, @org.jetbrains.annotations.Nullable
        java.lang.String angleSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String fourPointAngleSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String pointSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String lineSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String horizonLineSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String verticalLineSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String parallelLinesSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String threeVerticalSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String twoCirclesSelectedId) {
            return null;
        }
        
        /**
         * 🔒 选中状态保存数据类 - 用于删除操作保护
         */
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        /**
         * 🔒 选中状态保存数据类 - 用于删除操作保护
         */
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        /**
         * 🔒 选中状态保存数据类 - 用于删除操作保护
         */
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public SelectionState(@org.jetbrains.annotations.Nullable
        com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMeasurementMode, @org.jetbrains.annotations.Nullable
        java.lang.String angleSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String fourPointAngleSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String pointSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String lineSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String horizonLineSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String verticalLineSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String parallelLinesSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String threeVerticalSelectedId, @org.jetbrains.annotations.Nullable
        java.lang.String twoCirclesSelectedId) {
            super();
        }
        
        @org.jetbrains.annotations.Nullable
        public final com.touptek.measurerealize.MeasurementManager.MeasurementMode component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final com.touptek.measurerealize.MeasurementManager.MeasurementMode getActiveMeasurementMode() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getAngleSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getFourPointAngleSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getPointSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getLineSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getHorizonLineSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component7() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getVerticalLineSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component8() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getParallelLinesSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component9() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getThreeVerticalSelectedId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component10() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getTwoCirclesSelectedId() {
            return null;
        }
    }
    
    /**
     * 测量模式枚举
     */
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000f\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;", "", "(Ljava/lang/String;I)V", "ANGLE", "FOUR_POINT_ANGLE", "POINT", "LINE", "HORIZON_LINE", "VERTICAL_LINE", "PARALLEL_LINES", "THREE_VERTICAL", "RECTANGLE", "THREE_RECTANGLE", "ELLIPSE", "CIRCLE", "TWO_CIRCLES", "app_debug"})
    public static enum MeasurementMode {
        /*public static final*/ ANGLE /* = new ANGLE() */,
        /*public static final*/ FOUR_POINT_ANGLE /* = new FOUR_POINT_ANGLE() */,
        /*public static final*/ POINT /* = new POINT() */,
        /*public static final*/ LINE /* = new LINE() */,
        /*public static final*/ HORIZON_LINE /* = new HORIZON_LINE() */,
        /*public static final*/ VERTICAL_LINE /* = new VERTICAL_LINE() */,
        /*public static final*/ PARALLEL_LINES /* = new PARALLEL_LINES() */,
        /*public static final*/ THREE_VERTICAL /* = new THREE_VERTICAL() */,
        /*public static final*/ RECTANGLE /* = new RECTANGLE() */,
        /*public static final*/ THREE_RECTANGLE /* = new THREE_RECTANGLE() */,
        /*public static final*/ ELLIPSE /* = new ELLIPSE() */,
        /*public static final*/ CIRCLE /* = new CIRCLE() */,
        /*public static final*/ TWO_CIRCLES /* = new TWO_CIRCLES() */;
        
        MeasurementMode() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
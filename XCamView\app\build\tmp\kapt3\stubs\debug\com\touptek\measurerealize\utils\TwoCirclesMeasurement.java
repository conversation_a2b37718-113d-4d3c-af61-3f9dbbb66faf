package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 双圆测量实例 - 专业级同心圆数据结构
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b$\n\u0002\u0010\u0007\n\u0002\b%\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\f\b\u0086\b\u0018\u0000 k2\u00020\u0001:\u0001kB\u00cb\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0011\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u0019J\u0006\u00102\u001a\u00020\u0011J\u0006\u00103\u001a\u00020\u0011J\u0006\u00104\u001a\u00020\u0011J\u0006\u00105\u001a\u000206J\u0006\u00107\u001a\u00020\u0011J\u0006\u00108\u001a\u00020\u0011J\u0006\u00109\u001a\u00020\u0011J\u0006\u0010:\u001a\u000206J\u0006\u0010;\u001a\u00020\u0011J\u0006\u0010<\u001a\u00020\u0006J\t\u0010=\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010>\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\u0010\u0010@\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\u0010\u0010A\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\u0010\u0010B\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\u0010\u0010C\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\u0010\u0010D\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\u0010\u0010E\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010?J\t\u0010F\u001a\u00020\u000eH\u00c2\u0003J\u000f\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010I\u001a\u00020\tH\u00c6\u0003J\t\u0010J\u001a\u00020\tH\u00c6\u0003J\t\u0010K\u001a\u00020\tH\u00c6\u0003J\u000b\u0010L\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010M\u001a\u00020\u000eH\u00c6\u0003J\t\u0010N\u001a\u00020\u000eH\u00c6\u0003J\u001d\u0010O\u001a\u00020\u00062\u0006\u0010P\u001a\u00020\u00062\u0006\u0010Q\u001a\u00020RH\u0002\u00a2\u0006\u0002\u0010SJ\u001d\u0010T\u001a\u00020\u00062\u0006\u0010U\u001a\u00020\u00062\u0006\u0010Q\u001a\u00020RH\u0002\u00a2\u0006\u0002\u0010SJ\u00d4\u0001\u0010V\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0018\u001a\u00020\u000eH\u00c6\u0001\u00a2\u0006\u0002\u0010WJ\u0013\u0010X\u001a\u00020\t2\b\u0010Y\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010Z\u001a\u00020\u0003J\u000e\u0010[\u001a\u00020\\2\u0006\u0010]\u001a\u00020\u0006J\t\u0010^\u001a\u00020\\H\u00d6\u0001J\b\u0010_\u001a\u00020`H\u0002J\u0016\u0010a\u001a\u00020\t2\u0006\u0010]\u001a\u00020\u00062\u0006\u0010b\u001a\u000206J\u001e\u0010a\u001a\u00020\t2\u0006\u0010]\u001a\u00020\u00062\u0006\u0010c\u001a\u00020\\2\u0006\u0010b\u001a\u000206J\b\u0010d\u001a\u00020`H\u0002J\u0013\u0010e\u001a\u00020`2\u0006\u0010Q\u001a\u00020R\u00a2\u0006\u0002\u0010fJ\u0013\u0010g\u001a\u00020`2\u0006\u0010Q\u001a\u00020R\u00a2\u0006\u0002\u0010fJ\t\u0010h\u001a\u00020\u0003H\u00d6\u0001J\u0016\u0010i\u001a\u00020`2\u0006\u0010c\u001a\u00020\\2\u0006\u0010j\u001a\u00020\u0006R \u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u000e\u0010\u0018\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0013\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u0012\u0010\u0016\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u0012\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u0012\u0010\u0014\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u0012\u0010\u0017\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u0012\u0010\u0012\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u0012\u0010\u0015\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001eR\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u001a\u0010\u000b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010%\"\u0004\b&\u0010\'R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010%\"\u0004\b(\u0010\'R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010%\"\u0004\b)\u0010\'R\u001a\u0010\u000f\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b*\u0010 \"\u0004\b+\u0010\"R\u001c\u0010\f\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010-\"\u0004\b.\u0010/R \u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u0010\u001b\"\u0004\b1\u0010\u001d\u00a8\u0006l"}, d2 = {"Lcom/touptek/measurerealize/utils/TwoCirclesMeasurement;", "", "id", "", "viewPoints", "", "Landroid/graphics/PointF;", "bitmapPoints", "isSelected", "", "isEditing", "isCompleted", "textPosition", "creationTime", "", "lastModified", "cachedInnerRadius", "", "cachedOuterRadius", "cachedInnerArea", "cachedOuterArea", "cachedRingArea", "cachedInnerPerimeter", "cachedOuterPerimeter", "cacheValidTime", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;J)V", "getBitmapPoints", "()Ljava/util/List;", "setBitmapPoints", "(Ljava/util/List;)V", "Ljava/lang/Double;", "getCreationTime", "()J", "setCreationTime", "(J)V", "getId", "()Ljava/lang/String;", "()Z", "setCompleted", "(Z)V", "setEditing", "setSelected", "getLastModified", "setLastModified", "getTextPosition", "()Landroid/graphics/PointF;", "setTextPosition", "(Landroid/graphics/PointF;)V", "getViewPoints", "setViewPoints", "calculateInnerArea", "calculateInnerPerimeter", "calculateInnerRadius", "calculateInnerViewRadius", "", "calculateOuterArea", "calculateOuterPerimeter", "calculateOuterRadius", "calculateOuterViewRadius", "calculateRingArea", "calculateTextPosition", "component1", "component10", "()Ljava/lang/Double;", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "convertBitmapToViewCoords", "bitmapPoint", "imageView", "error/NonExistentClass", "(Landroid/graphics/PointF;Lerror/NonExistentClass;)Landroid/graphics/PointF;", "convertViewToBitmapCoords", "viewPoint", "copy", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;J)Lcom/touptek/measurerealize/utils/TwoCirclesMeasurement;", "equals", "other", "getDisplayText", "getNearestPointIndex", "", "touchPoint", "hashCode", "invalidateCache", "", "isPointInTouchRange", "touchRadius", "pointIndex", "markAsModified", "syncBitmapCoords", "(Lerror/NonExistentClass;)V", "syncViewCoords", "toString", "updateWithTwoCirclesConstraint", "newPoint", "Companion", "app_debug"})
public final class TwoCirclesMeasurement {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> viewPoints;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> bitmapPoints;
    private boolean isSelected;
    private boolean isEditing;
    private boolean isCompleted;
    @org.jetbrains.annotations.Nullable
    private android.graphics.PointF textPosition;
    private long creationTime;
    private long lastModified;
    private java.lang.Double cachedInnerRadius;
    private java.lang.Double cachedOuterRadius;
    private java.lang.Double cachedInnerArea;
    private java.lang.Double cachedOuterArea;
    private java.lang.Double cachedRingArea;
    private java.lang.Double cachedInnerPerimeter;
    private java.lang.Double cachedOuterPerimeter;
    private long cacheValidTime;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.TwoCirclesMeasurement.Companion Companion = null;
    private static final long CACHE_VALIDITY_MS = 100L;
    private static final double PI = java.lang.Math.PI;
    
    /**
     * 🎯 双圆测量实例 - 专业级同心圆数据结构
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.TwoCirclesMeasurement copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedRingArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerPerimeter, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterPerimeter, long cacheValidTime) {
        return null;
    }
    
    /**
     * 🎯 双圆测量实例 - 专业级同心圆数据结构
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🎯 双圆测量实例 - 专业级同心圆数据结构
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🎯 双圆测量实例 - 专业级同心圆数据结构
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public TwoCirclesMeasurement() {
        super();
    }
    
    public TwoCirclesMeasurement(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedRingArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerPerimeter, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterPerimeter, long cacheValidTime) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewPoints() {
        return null;
    }
    
    public final void setViewPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getBitmapPoints() {
        return null;
    }
    
    public final void setBitmapPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final void setEditing(boolean p0) {
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final void setCompleted(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final void setTextPosition(@org.jetbrains.annotations.Nullable
    android.graphics.PointF p0) {
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long getCreationTime() {
        return 0L;
    }
    
    public final void setCreationTime(long p0) {
    }
    
    public final long component9() {
        return 0L;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final void setLastModified(long p0) {
    }
    
    private final java.lang.Double component10() {
        return null;
    }
    
    private final java.lang.Double component11() {
        return null;
    }
    
    private final java.lang.Double component12() {
        return null;
    }
    
    private final java.lang.Double component13() {
        return null;
    }
    
    private final java.lang.Double component14() {
        return null;
    }
    
    private final java.lang.Double component15() {
        return null;
    }
    
    private final java.lang.Double component16() {
        return null;
    }
    
    private final long component17() {
        return 0L;
    }
    
    /**
     * 🎯 计算内圆半径（使用位图坐标）
     */
    public final double calculateInnerRadius() {
        return 0.0;
    }
    
    /**
     * 🎯 计算外圆半径（使用位图坐标）
     */
    public final double calculateOuterRadius() {
        return 0.0;
    }
    
    /**
     * 🎯 计算内圆视图半径（用于绘制）
     */
    public final float calculateInnerViewRadius() {
        return 0.0F;
    }
    
    /**
     * 🎯 计算外圆视图半径（用于绘制）
     */
    public final float calculateOuterViewRadius() {
        return 0.0F;
    }
    
    /**
     * 🎯 计算内圆面积
     */
    public final double calculateInnerArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算外圆面积
     */
    public final double calculateOuterArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算环形面积
     */
    public final double calculateRingArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算内圆周长
     */
    public final double calculateInnerPerimeter() {
        return 0.0;
    }
    
    /**
     * 🎯 计算外圆周长
     */
    public final double calculateOuterPerimeter() {
        return 0.0;
    }
    
    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, float touchRadius) {
        return false;
    }
    
    /**
     * 🎯 检查特定点是否在触摸范围内
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, int pointIndex, float touchRadius) {
        return false;
    }
    
    /**
     * 🎯 获取最近的控制点索引
     */
    public final int getNearestPointIndex(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return 0;
    }
    
    /**
     * 🎯 双圆约束更新方法 - 核心约束逻辑
     */
    public final void updateWithTwoCirclesConstraint(int pointIndex, @org.jetbrains.annotations.NotNull
    android.graphics.PointF newPoint) {
    }
    
    /**
     * 🔄 坐标转换方法 - 视图坐标转位图坐标
     */
    private final android.graphics.PointF convertViewToBitmapCoords(android.graphics.PointF viewPoint, error.NonExistentClass imageView) {
        return null;
    }
    
    /**
     * 🔄 坐标转换方法 - 位图坐标转视图坐标
     */
    private final android.graphics.PointF convertBitmapToViewCoords(android.graphics.PointF bitmapPoint, error.NonExistentClass imageView) {
        return null;
    }
    
    /**
     * 🔄 同步位图坐标（当需要保存时调用）
     */
    public final void syncBitmapCoords(@org.jetbrains.annotations.NotNull
    error.NonExistentClass imageView) {
    }
    
    /**
     * 🔄 同步视图坐标（缩放时调用）
     */
    public final void syncViewCoords(@org.jetbrains.annotations.NotNull
    error.NonExistentClass imageView) {
    }
    
    /**
     * 🎯 计算文本位置
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF calculateTextPosition() {
        return null;
    }
    
    /**
     * 更新最后修改时间
     */
    private final void markAsModified() {
    }
    
    /**
     * 清除缓存
     */
    private final void invalidateCache() {
    }
    
    /**
     * 获取双圆的显示文本
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0006\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/measurerealize/utils/TwoCirclesMeasurement$Companion;", "", "()V", "CACHE_VALIDITY_MS", "", "PI", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
package com.touptek.measurerealize.utils

import android.graphics.PointF
import android.util.Log
import android.view.ImageView
import android.view.MotionEvent
import com.touptek.xcamview.view.TpImageView
import java.util.*
import kotlin.math.*

/**
 * 🎯 双圆测量实例 - 专业级同心圆数据结构
 */
data class TwoCirclesMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [圆心, 内圆半径点, 外圆半径点] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [圆心, 内圆半径点, 外圆半径点] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis(),
    // 缓存计算结果以提高性能
    private var cachedInnerRadius: Double? = null,
    private var cachedOuterRadius: Double? = null,
    private var cachedInnerArea: Double? = null,
    private var cachedOuterArea: Double? = null,
    private var cachedRingArea: Double? = null,
    private var cachedInnerPerimeter: Double? = null,
    private var cachedOuterPerimeter: Double? = null,
    private var cacheValidTime: Long = 0L
) {
    companion object {
        private const val CACHE_VALIDITY_MS = 100L // 缓存有效期100ms
        private const val PI = Math.PI
    }

    /**
     * 🎯 计算内圆半径（使用位图坐标）
     */
    fun calculateInnerRadius(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedInnerRadius != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedInnerRadius!!
        }

        if (bitmapPoints.size < 2) return 0.0

        val center = bitmapPoints[0]
        val innerRadiusPoint = bitmapPoints[1]
        val dx = innerRadiusPoint.x - center.x
        val dy = innerRadiusPoint.y - center.y
        val radius = sqrt(dx * dx + dy * dy).toDouble()

        cachedInnerRadius = radius
        cacheValidTime = currentTime
        return radius
    }

    /**
     * 🎯 计算外圆半径（使用位图坐标）
     */
    fun calculateOuterRadius(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedOuterRadius != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedOuterRadius!!
        }

        if (bitmapPoints.size < 3) return 0.0

        val center = bitmapPoints[0]
        val outerRadiusPoint = bitmapPoints[2]
        val dx = outerRadiusPoint.x - center.x
        val dy = outerRadiusPoint.y - center.y
        val radius = sqrt(dx * dx + dy * dy).toDouble()

        cachedOuterRadius = radius
        cacheValidTime = currentTime
        return radius
    }

    /**
     * 🎯 计算内圆视图半径（用于绘制）
     */
    fun calculateInnerViewRadius(): Float {
        if (viewPoints.size < 2) return 0f

        val center = viewPoints[0]
        val innerRadiusPoint = viewPoints[1]
        val dx = innerRadiusPoint.x - center.x
        val dy = innerRadiusPoint.y - center.y
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 🎯 计算外圆视图半径（用于绘制）
     */
    fun calculateOuterViewRadius(): Float {
        if (viewPoints.size < 3) return 0f

        val center = viewPoints[0]
        val outerRadiusPoint = viewPoints[2]
        val dx = outerRadiusPoint.x - center.x
        val dy = outerRadiusPoint.y - center.y
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 🎯 计算内圆面积
     */
    fun calculateInnerArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedInnerArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedInnerArea!!
        }

        val radius = calculateInnerRadius()
        val area = PI * radius * radius

        cachedInnerArea = area
        return area
    }

    /**
     * 🎯 计算外圆面积
     */
    fun calculateOuterArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedOuterArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedOuterArea!!
        }

        val radius = calculateOuterRadius()
        val area = PI * radius * radius

        cachedOuterArea = area
        return area
    }

    /**
     * 🎯 计算环形面积
     */
    fun calculateRingArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedRingArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedRingArea!!
        }

        val outerArea = calculateOuterArea()
        val innerArea = calculateInnerArea()
        val ringArea = outerArea - innerArea

        cachedRingArea = ringArea
        return ringArea
    }

    /**
     * 🎯 计算内圆周长
     */
    fun calculateInnerPerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedInnerPerimeter != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedInnerPerimeter!!
        }

        val radius = calculateInnerRadius()
        val perimeter = 2.0 * PI * radius

        cachedInnerPerimeter = perimeter
        return perimeter
    }

    /**
     * 🎯 计算外圆周长
     */
    fun calculateOuterPerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedOuterPerimeter != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedOuterPerimeter!!
        }

        val radius = calculateOuterRadius()
        val perimeter = 2.0 * PI * radius

        cachedOuterPerimeter = perimeter
        return perimeter
    }

    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        return viewPoints.any { point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            distance <= touchRadius
        }
    }

    /**
     * 🎯 检查特定点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex < 0 || pointIndex >= viewPoints.size) return false
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }

    /**
     * 🎯 获取最近的控制点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        if (viewPoints.isEmpty()) return -1
        
        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE
        
        viewPoints.forEachIndexed { index, point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }

    /**
     * 🎯 双圆约束更新方法 - 核心约束逻辑
     */
    fun updateWithTwoCirclesConstraint(pointIndex: Int, newPoint: PointF) {
        when (pointIndex) {
            0 -> { // 圆心移动 - 整个双圆组合平移
                val deltaX = newPoint.x - viewPoints[0].x
                val deltaY = newPoint.y - viewPoints[0].y
                viewPoints[0] = newPoint
                if (viewPoints.size > 1) {
                    viewPoints[1] = PointF(viewPoints[1].x + deltaX, viewPoints[1].y + deltaY)
                }
                if (viewPoints.size > 2) {
                    viewPoints[2] = PointF(viewPoints[2].x + deltaX, viewPoints[2].y + deltaY)
                }
            }
            1 -> { // 内圆半径点移动 - 调整内圆大小，确保不超过外圆
                val center = viewPoints[0]
                val dx = newPoint.x - center.x
                val dy = newPoint.y - center.y
                val newInnerRadius = sqrt(dx * dx + dy * dy)
                
                // 确保内圆半径不超过外圆半径
                val outerRadius = if (viewPoints.size > 2) {
                    val outerPoint = viewPoints[2]
                    val outerDx = outerPoint.x - center.x
                    val outerDy = outerPoint.y - center.y
                    sqrt(outerDx * outerDx + outerDy * outerDy)
                } else {
                    Float.MAX_VALUE
                }
                
                if (newInnerRadius <= outerRadius - 10f) { // 保持至少10px的间距
                    viewPoints[1] = newPoint
                }
            }
            2 -> { // 外圆半径点移动 - 调整外圆大小，确保不小于内圆
                val center = viewPoints[0]
                val dx = newPoint.x - center.x
                val dy = newPoint.y - center.y
                val newOuterRadius = sqrt(dx * dx + dy * dy)
                
                // 确保外圆半径不小于内圆半径
                val innerRadius = if (viewPoints.size > 1) {
                    val innerPoint = viewPoints[1]
                    val innerDx = innerPoint.x - center.x
                    val innerDy = innerPoint.y - center.y
                    sqrt(innerDx * innerDx + innerDy * innerDy)
                } else {
                    0f
                }
                
                if (newOuterRadius >= innerRadius + 10f) { // 保持至少10px的间距
                    viewPoints[2] = newPoint
                }
            }
        }
        invalidateCache()
        markAsModified()
    }

    /**
     * 🔄 坐标转换方法 - 视图坐标转位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = android.graphics.Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🔄 坐标转换方法 - 位图坐标转视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * 🔄 同步位图坐标（当需要保存时调用）
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
        invalidateCache()
    }

    /**
     * 🔄 同步视图坐标（缩放时调用）
     */
    fun syncViewCoords(imageView: ImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
        invalidateCache()
    }

    /**
     * 🎯 计算文本位置
     */
    fun calculateTextPosition(): PointF {
        if (viewPoints.size < 3) return PointF(0f, 0f)
        val center = viewPoints[0]
        val outerRadius = calculateOuterViewRadius()
        // 文本位置在外圆下方
        return PointF(center.x, center.y + outerRadius + 50f)
    }

    /**
     * 更新最后修改时间
     */
    private fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 清除缓存
     */
    private fun invalidateCache() {
        cachedInnerRadius = null
        cachedOuterRadius = null
        cachedInnerArea = null
        cachedOuterArea = null
        cachedRingArea = null
        cachedInnerPerimeter = null
        cachedOuterPerimeter = null
        cacheValidTime = 0L
    }

    /**
     * 获取双圆的显示文本
     */
    fun getDisplayText(): String {
        val innerRadius = calculateInnerRadius()
        val outerRadius = calculateOuterRadius()
        val innerArea = calculateInnerArea()
        val outerArea = calculateOuterArea()
        val ringArea = calculateRingArea()
        val innerPerimeter = calculateInnerPerimeter()
        val outerPerimeter = calculateOuterPerimeter()

        return """内圆：周长：%.1f px，面积：%.1f px²
外圆：周长：%.1f px，面积：%.1f px²
环形面积：%.1f px²""".format(innerPerimeter, innerArea, outerPerimeter, outerArea, ringArea)
    }
}

/**
 * 🎯 双圆测量助手 - 专业级同心圆测量系统
 */
class TwoCirclesMeasureHelper {
    companion object {
        private const val TAG = "TwoCirclesMeasureHelper"
        private const val TOUCH_RADIUS = 80f // 触摸检测半径
        private const val LONG_PRESS_DURATION = 800L // 长按删除时间
        private const val CLICK_TOLERANCE = 30f // 点击容差
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var bitmap: android.graphics.Bitmap
    private var isInitialized = false

    // 测量数据
    private val measurements = mutableListOf<TwoCirclesMeasurement>()
    private var selectedMeasurement: TwoCirclesMeasurement? = null
    private var activeMeasurement: TwoCirclesMeasurement? = null

    // 交互状态
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: android.graphics.Bitmap) {
        this.imageView = imageView
        this.bitmap = bitmap
        this.isInitialized = true
        Log.d(TAG, "🚀 TwoCirclesMeasureHelper initialized")
    }

    /**
     * 🔄 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎯 开始新的双圆测量 - 在屏幕中央创建同心圆
     */
    fun startNewMeasurement(): String {
        // 在视图中心生成双圆
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val innerRadius = minOf(imageView.width, imageView.height) / 12f // 内圆半径
        val outerRadius = minOf(imageView.width, imageView.height) / 8f  // 外圆半径

        // 在视图坐标系中创建双圆的控制点
        val centerPoint = PointF(viewCenterX, viewCenterY)
        val innerRadiusPoint = PointF(viewCenterX + innerRadius, viewCenterY)
        val outerRadiusPoint = PointF(viewCenterX + outerRadius, viewCenterY)

        // 创建完整的测量
        val measurement = TwoCirclesMeasurement(
            isSelected = true,
            isEditing = false,  // 直接设为完成状态，可拖动
            isCompleted = true
        )

        // 添加视图坐标点（圆心、内圆半径点、外圆半径点）
        measurement.viewPoints.addAll(listOf(centerPoint, innerRadiusPoint, outerRadiusPoint))

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        // 设置文本位置
        measurement.textPosition = measurement.calculateTextPosition()

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        activeMeasurement = null
        selectedMeasurement = measurement

        notifyUpdate()
        Log.d(TAG, "🎯 Created new two circles measurement - inner: ${String.format("%.1f", measurement.calculateInnerRadius())}px, outer: ${String.format("%.1f", measurement.calculateOuterRadius())}px")
        return measurement.id
    }

    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<TwoCirclesMeasurement> {
        return measurements.toList()
    }

    /**
     * 🎯 处理触摸事件 - 标准模式实现
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                Log.d(TAG, "🎯 ACTION_DOWN at ($x, $y)")
                return handleTouchDown(touchPoint, viewWidth, viewHeight)
            }

            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(touchPoint)
            }

            MotionEvent.ACTION_UP -> {
                return handleTouchUp(touchPoint)
            }
        }

        return false
    }

    private fun handleTouchDown(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        // 记录触摸起始信息
        longPressStartTime = System.currentTimeMillis()
        lastTouchX = touchPoint.x
        lastTouchY = touchPoint.y

        // 检查控制点触摸（优先级：圆心 > 外圆半径点 > 内圆半径点）
        for (measurement in measurements.reversed()) {
            if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                val nearestPointIndex = measurement.getNearestPointIndex(touchPoint)
                if (nearestPointIndex >= 0) {
                    selectedMeasurement = measurement
                    draggedPointIndex = nearestPointIndex
                    isDraggingPoint = true

                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true

                    val pointName = when(nearestPointIndex) {
                        0 -> "center"
                        1 -> "inner radius point"
                        2 -> "outer radius point"
                        else -> "unknown"
                    }
                    Log.d(TAG, "🎯 Started dragging $pointName (index $nearestPointIndex) of measurement ${measurement.id}")
                    notifyUpdate()
                    return true
                }
            }
        }

        // 空白区域点击处理
        if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
            measurements.forEach { it.isSelected = false }
            selectedMeasurement = null
            Log.d(TAG, "🎯 Clicked in empty area, cleared selection")
            notifyUpdate()
        }

        return false
    }

    private fun handleTouchMove(touchPoint: PointF): Boolean {
        if (isDraggingPoint) {
            selectedMeasurement?.let { measurement ->
                if (draggedPointIndex < measurement.viewPoints.size) {
                    // 使用双圆约束更新点位置
                    measurement.updateWithTwoCirclesConstraint(draggedPointIndex, touchPoint)

                    // 同步位图坐标
                    measurement.syncBitmapCoords(imageView)

                    // 更新文本位置
                    measurement.textPosition = measurement.calculateTextPosition()

                    notifyUpdate()
                    return true
                }
            }
        }
        return false
    }

    private fun handleTouchUp(touchPoint: PointF): Boolean {
        val touchDuration = System.currentTimeMillis() - longPressStartTime
        val touchDistance = sqrt((touchPoint.x - lastTouchX) * (touchPoint.x - lastTouchX) + (touchPoint.y - lastTouchY) * (touchPoint.y - lastTouchY))
        val wasDragging = isDraggingPoint

        var handled = false

        // 重置拖拽状态
        isDraggingPoint = false
        draggedPointIndex = -1

        if (wasDragging) {
            // 拖拽完成
            Log.d(TAG, "🎯 Finished dragging")
            notifyUpdate()
            handled = true
        }

        // 长按删除
        if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                    measurements.remove(measurement)
                    if (selectedMeasurement == measurement) {
                        selectedMeasurement = null
                    }
                    Log.d(TAG, "🗑️ Long press deleted measurement ${measurement.id}")
                    notifyUpdate()
                    handled = true
                    break
                }
            }
        }

        // 轻触选中
        if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true
                    selectedMeasurement = measurement
                    Log.d(TAG, "🎯 Selected measurement ${measurement.id}")
                    notifyUpdate()
                    return true
                }
            }
        }

        return handled
    }

    /**
     * 🎯 检查是否在图像内容区域（避免UI按钮区域）
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 🎯 通知更新
     */
    private fun notifyUpdate() {
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
            measurement.textPosition = measurement.calculateTextPosition()
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Synced coordinates after scale change")
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 获取选中的测量
     */
    fun getSelectedMeasurement(): TwoCirclesMeasurement? = selectedMeasurement

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean = selectedMeasurement != null

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * 🎯 删除选中的测量 - 智能删除逻辑
     */
    fun deleteSelectedMeasurement(): Boolean {
        var selected = selectedMeasurement

        // 如果没有选中的测量，尝试自动选中最后一个测量
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        val removed = measurements.remove(selected)
        if (removed) {
            selectedMeasurement = null
            resetInteractionState()

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            }

            notifyUpdate()
            Log.d(TAG, "✅ Deleted measurement: ${selected.id}")
            return true
        }
        return false
    }

    /**
     * 🎯 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        activeMeasurement = null
    }

    /**
     * 🎯 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        notifyUpdate()
    }

    /**
     * 🎯 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..2) { // 双圆有三个点：中心点(0)、内圆半径点(1)、外圆半径点(2)
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    return true
                }
            }
            false
        }
    }

    /**
     * 🎯 检查点是否在测量上（用于混合触摸处理器）
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..2) { // 双圆有三个点：中心点(0)、内圆半径点(1)、外圆半径点(2)
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    return true
                }
            }
            false
        }
    }

    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    fun pauseMeasurement() {
        Log.d(TAG, "⏸️ Measurement paused - data preserved")
    }

    /**
     * 🎯 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    fun resumeMeasurement() {
        Log.d(TAG, "▶️ Measurement resumed - ready for continued editing")
    }

    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        measurementUpdateCallback = null
        isInitialized = false
        Log.d(TAG, "🧹 TwoCirclesMeasureHelper cleaned up")
    }
}
